# Elixir
/_build/
/deps/
/.elixir_ls/
/cover/
erl_crash.dump

# Phoenix
/priv/static/
/priv/static/assets/
/priv/static/cache_manifest.json

# Node.js
/assets/node_modules/
npm-debug.log

# Environment variables
.env
.env.*
!.env.example

# IDE and Editor files
.idea/
.vscode/
*.swp
*~

# Test artifacts
/test/tmp/
/test/screenshots/

# Database
*.db
*.dump

# Temporary files
/tmp/
.DS_Store

# Keep git HEAD and refs but ignore the rest
.git/*
!.git/HEAD
!.git/refs/
