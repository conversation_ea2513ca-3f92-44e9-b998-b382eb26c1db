{<<"app">>,<<"cowboy_telemetry">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,<<"Telemetry instrumentation for Cowboy">>}.
{<<"files">>,
 [<<"LICENSE">>,<<"README.md">>,<<"rebar.config">>,<<"rebar.lock">>,
  <<"src/cowboy_telemetry.app.src">>,<<"src/cowboy_telemetry_h.erl">>]}.
{<<"licenses">>,[<<"Apache 2.0">>]}.
{<<"links">>,
 [{<<"Github">>,<<"https://github.com/beam-telemetry/cowboy_telemetry">>}]}.
{<<"name">>,<<"cowboy_telemetry">>}.
{<<"requirements">>,
 [{<<"cowboy">>,
   [{<<"app">>,<<"cowboy">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~> 2.7">>}]},
  {<<"telemetry">>,
   [{<<"app">>,<<"telemetry">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~> 1.0">>}]}]}.
{<<"version">>,<<"0.4.0">>}.
