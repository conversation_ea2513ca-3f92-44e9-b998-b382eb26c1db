{<<"links">>,
 [{<<"Changelog">>,
   <<"https://github.com/elixir-gettext/expo/blob/main/CHANGELOG.md">>},
  {<<"GitHub">>,<<"https://github.com/elixir-gettext/expo">>},
  {<<"Issues">>,<<"https://github.com/elixir-gettext/expo/issues">>}]}.
{<<"name">>,<<"expo">>}.
{<<"version">>,<<"1.1.0">>}.
{<<"description">>,
 <<"Low-level Gettext file handling (.po/.pot/.mo file writer and parser).">>}.
{<<"elixir">>,<<"~> 1.11">>}.
{<<"app">>,<<"expo">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"files">>,
 [<<"lib">>,<<"lib/expo">>,<<"lib/expo/messages.ex">>,<<"lib/expo/mo">>,
  <<"lib/expo/mo/invalid_file_error.ex">>,
  <<"lib/expo/mo/unsupported_version_error.ex">>,
  <<"lib/expo/mo/composer.ex">>,<<"lib/expo/mo/parser.ex">>,
  <<"lib/expo/plural_forms.ex">>,<<"lib/expo/message.ex">>,
  <<"lib/expo/message">>,<<"lib/expo/message/singular.ex">>,
  <<"lib/expo/message/plural.ex">>,<<"lib/expo/plural_forms">>,
  <<"lib/expo/plural_forms/tokenizer.ex">>,
  <<"lib/expo/plural_forms/known.ex">>,
  <<"lib/expo/plural_forms/syntax_error.ex">>,<<"lib/expo/po">>,
  <<"lib/expo/po/duplicate_translations_error.ex">>,
  <<"lib/expo/po/tokenizer.ex">>,<<"lib/expo/po/composer.ex">>,
  <<"lib/expo/po/parser.ex">>,<<"lib/expo/po/syntax_error.ex">>,
  <<"lib/expo/util.ex">>,<<"lib/expo/mo.ex">>,<<"lib/expo/po.ex">>,
  <<"lib/mix">>,<<"lib/mix/tasks">>,<<"lib/mix/tasks/expo.msguniq.ex">>,
  <<"lib/mix/tasks/expo.msgmft.ex">>,<<".formatter.exs">>,<<"mix.exs">>,
  <<"README.md">>,<<"LICENSE">>,<<"CHANGELOG.md">>,<<"src">>,
  <<"src/expo_plural_forms_parser.erl">>,
  <<"src/expo_plural_forms_parser.yrl">>,<<"src/expo_po_parser.yrl">>,
  <<"src/expo_po_parser.erl">>]}.
{<<"build_tools">>,[<<"mix">>]}.
