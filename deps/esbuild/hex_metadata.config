{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/phoenixframework/esbuild">>},
  {<<"esbuild">>,<<"https://esbuild.github.io">>}]}.
{<<"name">>,<<"esbuild">>}.
{<<"version">>,<<"0.10.0">>}.
{<<"description">>,<<"Mix tasks for installing and invoking esbuild">>}.
{<<"elixir">>,<<"~> 1.14">>}.
{<<"app">>,<<"esbuild">>}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"jason">>},
   {<<"app">>,<<"jason">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.4">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"files">>,
 [<<"lib">>,<<"lib/esbuild.ex">>,<<"lib/mix">>,<<"lib/mix/tasks">>,
  <<"lib/mix/tasks/esbuild.ex">>,<<"lib/mix/tasks/esbuild.install.ex">>,
  <<"lib/esbuild">>,<<"lib/esbuild/npm_registry.ex">>,<<".formatter.exs">>,
  <<"mix.exs">>,<<"README.md">>,<<"LICENSE.md">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.
