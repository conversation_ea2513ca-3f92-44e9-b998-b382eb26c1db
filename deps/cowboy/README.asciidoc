= Cowboy

Cowboy is a small, fast and modern HTTP server for Erlang/OTP.

== Goals

Cowboy aims to provide a *complete* HTTP stack in a *small* code base.
It is optimized for *low latency* and *low memory usage*, in part
because it uses *binary strings*.

<PERSON> provides *routing* capabilities, selectively dispatching requests
to handlers written in Erlang.

Because it uses Ranch for managing connections, <PERSON> can easily be
*embedded* in any other application.

<PERSON> is *clean* and *well tested* Erlang code.

== Online documentation

* https://ninenines.eu/docs/en/cowboy/2.13/guide[User guide]
* https://ninenines.eu/docs/en/cowboy/2.13/manual[Function reference]

== Offline documentation

* While still online, run `make docs`
* User guide available in `doc/` in PDF and HTML formats
* Function reference man pages available in `doc/man3/` and `doc/man7/`
* Run `make install-docs` to install man pages on your system
* Full documentation in Asciidoc available in `doc/src/`
* Examples available in `examples/`

== Getting help

* https://discord.gg/x25nNq2fFE[Discord server]
* https://github.com/ninenines/cowboy/issues[Issues tracker]
* https://ninenines.eu/services[Commercial Support]
* https://github.com/sponsors/essen[Sponsor me!]
