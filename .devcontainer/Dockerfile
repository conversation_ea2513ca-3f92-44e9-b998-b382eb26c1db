# Phoenix Elixir Development Container with Kubernetes Tools
FROM elixir:1.15-alpine

# Install system dependencies
RUN apk add --no-cache \
    build-base \
    git \
    curl \
    wget \
    bash \
    zsh \
    openssh-client \
    postgresql-client \
    nodejs \
    npm \
    python3 \
    py3-pip \
    ca-certificates \
    openssl \
    ncurses-libs \
    inotify-tools \
    sudo \
    shadow

# Install Docker CLI
RUN apk add --no-cache docker-cli

# Install GitHub CLI (Alpine-compatible)
RUN apk add --no-cache github-cli

# Install kubectl
RUN curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" \
    && chmod +x kubectl \
    && mv kubectl /usr/local/bin/

# Install Helm
RUN curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# Install k9s (Kubernetes CLI UI)
RUN wget -qO- https://github.com/derailed/k9s/releases/latest/download/k9s_Linux_amd64.tar.gz | tar xvz -C /tmp \
    && mv /tmp/k9s /usr/local/bin/

# Install kubectx and kubens
RUN git clone https://github.com/ahmetb/kubectx /opt/kubectx \
    && ln -s /opt/kubectx/kubectx /usr/local/bin/kubectx \
    && ln -s /opt/kubectx/kubens /usr/local/bin/kubens

# Install kustomize
RUN curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash \
    && mv kustomize /usr/local/bin/

# Install Elixir and Phoenix tools
RUN mix local.hex --force \
    && mix local.rebar --force \
    && mix archive.install hex phx_new --force

# Create vscode user with proper shell setup
RUN addgroup -g 1000 vscode \
    && adduser -u 1000 -G vscode -s /bin/bash -D vscode \
    && echo "vscode ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers \
    && mkdir -p /home/<USER>/.local/bin \
    && chown -R vscode:vscode /home/<USER>

# Set up workspace directory
RUN mkdir -p /workspace \
    && chown -R vscode:vscode /workspace

# Install additional development tools
RUN npm install -g @tailwindcss/cli

# Install useful CLI tools
RUN apk add --no-cache \
    jq \
    yq \
    tree \
    htop \
    vim \
    nano

# Set up shell for vscode user
USER vscode
WORKDIR /workspace

# Install Oh My Zsh for vscode user
RUN sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended

# Configure shell aliases and environment
RUN echo 'alias ll="ls -la"' >> ~/.bashrc \
    && echo 'alias k="kubectl"' >> ~/.bashrc \
    && echo 'alias kgp="kubectl get pods"' >> ~/.bashrc \
    && echo 'alias kgs="kubectl get services"' >> ~/.bashrc \
    && echo 'alias kgd="kubectl get deployments"' >> ~/.bashrc \
    && echo 'alias kdp="kubectl describe pod"' >> ~/.bashrc \
    && echo 'alias kds="kubectl describe service"' >> ~/.bashrc \
    && echo 'alias kdd="kubectl describe deployment"' >> ~/.bashrc \
    && echo 'alias klf="kubectl logs -f"' >> ~/.bashrc \
    && echo 'alias kpf="kubectl port-forward"' >> ~/.bashrc \
    && echo 'alias mix-test="mix test --color"' >> ~/.bashrc \
    && echo 'alias mix-deps="mix deps.get && mix deps.compile"' >> ~/.bashrc \
    && echo 'alias phx-server="mix phx.server"' >> ~/.bashrc \
    && echo 'alias phx-routes="mix phx.routes"' >> ~/.bashrc

# Set up Elixir Language Server cache directory
RUN mkdir -p ~/.elixir_ls

# Configure git (will be overridden by user's git config)
RUN git config --global init.defaultBranch main \
    && git config --global pull.rebase false

# Set default shell
ENV SHELL=/bin/bash

# Expose common ports
EXPOSE 4000 5432 6379 9090

# Keep container running
CMD ["sleep", "infinity"]
