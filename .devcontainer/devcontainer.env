# Phoenix Elixir Development Environment Variables
# This file contains environment variables for the development container

# Phoenix Configuration
MIX_ENV=dev
PHX_HOST=localhost
PORT=4000

# Database Configuration
DATABASE_URL=ecto://postgres:postgres@postgres:5432/phoenix_dev
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_HOSTNAME=postgres
DB_PORT=5432
DB_NAME=phoenix_dev
DB_POOL_SIZE=10

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DATABASE=0

# Development Secrets (NOT FOR PRODUCTION!)
SECRET_KEY_BASE=dev-secret-key-base-change-in-production-this-is-only-for-development-use
LIVE_VIEW_SIGNING_SALT=phoenix_live_view_dev_salt
GUARDIAN_SECRET_KEY=dev-guardian-secret-key-for-development-only
JWT_SECRET=dev-jwt-secret-for-development-only
ENCRYPTION_KEY=dev-encryption-key-32-characters

# Application Settings
LOG_LEVEL=debug
ENABLE_TELEMETRY=true
HEALTH_CHECK_PATH=/health
READY_CHECK_PATH=/ready

# Cache Configuration
CACHE_ADAPTER=Nebulex.Adapters.Redis
CACHE_TTL=3600

# Development Features
RATE_LIMIT_ENABLED=false
SCHEDULER_ENABLED=true
PROMETHEUS_METRICS_ENABLED=true
METRICS_PORT=9090

# CORS Configuration
CORS_ORIGINS=http://localhost:4000

# Session Configuration
SESSION_TIMEOUT=86400

# LiveView Configuration
LIVE_VIEW_SIGNING_SALT=phoenix_live_view_dev

# Email Configuration (for development)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USERNAME=
SMTP_PASSWORD=

# File Upload Configuration
UPLOAD_PATH=/tmp/uploads
MAX_UPLOAD_SIZE=10485760

# External API Configuration (for development)
EXTERNAL_API_URL=https://api.example.com
EXTERNAL_API_KEY=dev-api-key

# Kubernetes Configuration
KUBECONFIG=/home/<USER>/.kube/config
KUBECTL_CONTEXT=kind-kind

# Development Tools
EDITOR=code
BROWSER=none
