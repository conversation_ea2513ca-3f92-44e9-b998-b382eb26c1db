# DevContainer Troubleshooting Guide

This guide helps resolve common issues when using the Phoenix Elixir development container.

## 🚨 Common Issues and Solutions

### Container Build Failures

#### Issue: "Permission denied" when creating VS Code server directories
**Problem**: VS Code server cannot create directories in `/home/<USER>/.vscode-server/bin`.

**Symptoms**:
```
mkdir: can't create directory '/home/<USER>/.vscode-server/bin': Permission denied
```

**Solutions**:
1. **Rebuild the container** (recommended):
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Dev Containers: Rebuild Container"
   - Select "Rebuild Without Cache"

2. **Fix permissions manually**:
   ```bash
   # Run the permission fix script
   .devcontainer/scripts/fix-permissions.sh

   # Or manually fix permissions
   sudo chown -R vscode:vscode /home/<USER>
   chmod -R 755 /home/<USER>
   ```

3. **For Apple Silicon (ARM64) Macs**:
   - Ensure Docker Desktop is updated to the latest version
   - Check Docker Desktop → Settings → General → "Use Rosetta for x86/amd64 emulation"

#### Issue: "Feature failed to install" errors
**Problem**: Incompatible features for Alpine Linux base image.

**Solution**: The devcontainer has been updated to use Alpine-compatible installations. If you see feature installation errors:

1. **Rebuild the container**:
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Dev Containers: Rebuild Container"
   - Select "Rebuild Without Cache"

2. **Check Docker resources**:
   - Ensure Docker Desktop has sufficient memory (4GB+ recommended)
   - Ensure sufficient disk space (10GB+ free)

#### Issue: "apt-get: command not found"
**Problem**: Trying to use Debian/Ubuntu commands on Alpine Linux.

**Solution**: This has been fixed in the current configuration. All tools are installed using Alpine's `apk` package manager.

### Service Connection Issues

#### Issue: PostgreSQL connection refused
**Symptoms**:
```
could not connect to server: Connection refused
```

**Solutions**:
1. **Wait for services to start**:
   ```bash
   # Check if PostgreSQL is ready
   pg_isready -h postgres -p 5432 -U postgres
   ```

2. **Restart the container**:
   - Press `Ctrl+Shift+P` → "Dev Containers: Rebuild Container"

3. **Check service logs**:
   ```bash
   docker-compose -f .devcontainer/docker-compose.yml logs postgres
   ```

#### Issue: Redis connection refused
**Symptoms**:
```
Error connecting to Redis
```

**Solutions**:
1. **Check Redis status**:
   ```bash
   redis-cli -h redis ping
   ```

2. **Restart services**:
   ```bash
   docker-compose -f .devcontainer/docker-compose.yml restart redis
   ```

### Elixir/Phoenix Issues

#### Issue: "mix: command not found"
**Problem**: Elixir tools not available in PATH.

**Solutions**:
1. **Reload shell**:
   ```bash
   source ~/.bashrc
   ```

2. **Check Elixir installation**:
   ```bash
   which elixir
   elixir --version
   ```

3. **Reinstall Elixir tools**:
   ```bash
   mix local.hex --force
   mix local.rebar --force
   ```

#### Issue: Dependencies won't compile
**Symptoms**:
```
** (Mix) Could not compile dependency
```

**Solutions**:
1. **Clean and reinstall dependencies**:
   ```bash
   mix deps.clean --all
   mix deps.get
   mix deps.compile
   ```

2. **Check for missing system dependencies**:
   ```bash
   apk add --no-cache build-base
   ```

### Kubernetes Tools Issues

#### Issue: kubectl not working
**Solutions**:
1. **Verify installation**:
   ```bash
   kubectl version --client
   ```

2. **Check PATH**:
   ```bash
   echo $PATH
   which kubectl
   ```

3. **Reinstall if needed**:
   ```bash
   curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
   chmod +x kubectl
   sudo mv kubectl /usr/local/bin/
   ```

#### Issue: k9s crashes or won't start
**Solutions**:
1. **Check terminal compatibility**:
   ```bash
   export TERM=xterm-256color
   k9s
   ```

2. **Update k9s**:
   ```bash
   # Download latest version
   wget -qO- https://github.com/derailed/k9s/releases/latest/download/k9s_Linux_amd64.tar.gz | tar xvz -C /tmp
   sudo mv /tmp/k9s /usr/local/bin/
   ```

### VS Code Integration Issues

#### Issue: Elixir Language Server not working
**Solutions**:
1. **Restart Language Server**:
   - Press `Ctrl+Shift+P` → "Elixir LS: Restart"

2. **Clear Language Server cache**:
   ```bash
   rm -rf ~/.elixir_ls
   ```

3. **Check extension installation**:
   - Go to Extensions tab
   - Ensure "ElixirLS: Elixir support and debugger" is installed and enabled

#### Issue: Extensions not loading
**Solutions**:
1. **Reload window**:
   - Press `Ctrl+Shift+P` → "Developer: Reload Window"

2. **Check extension installation**:
   - Extensions should auto-install when container starts
   - Manually install if needed from Extensions marketplace

### Performance Issues

#### Issue: Slow container startup
**Solutions**:
1. **Increase Docker resources**:
   - Docker Desktop → Settings → Resources
   - Increase Memory to 6GB+
   - Increase CPU cores to 4+

2. **Use volume caches**:
   - Already configured in docker-compose.yml
   - Volumes persist dependencies between container rebuilds

#### Issue: Slow compilation
**Solutions**:
1. **Use incremental compilation**:
   ```bash
   export MIX_ENV=dev
   mix compile
   ```

2. **Parallel compilation**:
   ```bash
   export ELIXIR_MAKE_JOBS=4
   mix deps.compile
   ```

## 🔧 Advanced Troubleshooting

### Debug Container Build
```bash
# Build with verbose output
docker-compose -f .devcontainer/docker-compose.yml build --no-cache --progress=plain

# Check container logs
docker-compose -f .devcontainer/docker-compose.yml logs phoenix-dev
```

### Reset Everything
If all else fails, completely reset the development environment:

```bash
# Stop and remove containers
docker-compose -f .devcontainer/docker-compose.yml down -v

# Remove images
docker rmi $(docker images -q "*phoenix*")

# Rebuild from scratch
# In VS Code: Ctrl+Shift+P → "Dev Containers: Rebuild Container"
```

### Check System Resources
```bash
# Check memory usage
free -h

# Check disk space
df -h

# Check running processes
ps aux | head -20
```

## 📞 Getting Help

If you're still experiencing issues:

1. **Check the logs**:
   - Container logs: `docker-compose logs`
   - VS Code logs: Help → Toggle Developer Tools → Console

2. **Verify system requirements**:
   - Docker Desktop 4.0+
   - VS Code with Dev Containers extension
   - 8GB+ RAM recommended
   - 20GB+ free disk space

3. **Create an issue**:
   - Include error messages
   - Include system information
   - Include steps to reproduce

## 🚀 Performance Tips

- **Use SSD storage** for better I/O performance
- **Close unnecessary applications** to free up memory
- **Use Docker Desktop's experimental features** for better performance
- **Enable BuildKit** for faster Docker builds: `export DOCKER_BUILDKIT=1`

---

**Most issues can be resolved by rebuilding the container or restarting Docker Desktop.** 🔄
