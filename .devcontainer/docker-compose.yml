version: '3.8'

services:
  # Phoenix development container
  phoenix-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: phoenix-dev-container
    
    # Keep container running
    command: sleep infinity
    
    # Environment variables for development
    environment:
      # Phoenix configuration
      MIX_ENV: dev
      PHX_HOST: localhost
      PORT: "4000"
      
      # Database configuration
      DATABASE_URL: "ecto://postgres:postgres@postgres:5432/phoenix_dev"
      DB_USERNAME: postgres
      DB_PASSWORD: postgres
      DB_HOSTNAME: postgres
      DB_PORT: "5432"
      DB_NAME: phoenix_dev
      
      # Redis configuration
      REDIS_HOST: redis
      REDIS_PORT: "6379"
      REDIS_DATABASE: "0"
      
      # Development secrets (not for production!)
      SECRET_KEY_BASE: "dev-secret-key-base-change-in-production-this-is-only-for-development"
      LIVE_VIEW_SIGNING_SALT: "phoenix_live_view_dev"
      GUARDIAN_SECRET_KEY: "dev-guardian-secret-key"
      JWT_SECRET: "dev-jwt-secret"
      ENCRYPTION_KEY: "dev-encryption-key-32-characters"
      
      # Application settings
      LOG_LEVEL: debug
      ENABLE_TELEMETRY: "true"
      HEALTH_CHECK_PATH: "/health"
      READY_CHECK_PATH: "/ready"
      
      # Cache configuration
      CACHE_ADAPTER: "Nebulex.Adapters.Redis"
      CACHE_TTL: "3600"
      
      # Development features
      RATE_LIMIT_ENABLED: "false"
      SCHEDULER_ENABLED: "true"
      PROMETHEUS_METRICS_ENABLED: "true"
      METRICS_PORT: "9090"
      
      # CORS for development
      CORS_ORIGINS: "http://localhost:4000"
      
      # Session configuration
      SESSION_TIMEOUT: "86400"
    
    # Port mappings
    ports:
      - "4000:4000"   # Phoenix application
      - "9090:9090"   # Metrics/Prometheus
    
    # Volume mounts
    volumes:
      - ..:/workspace:cached
      - phoenix-dev-mix-cache:/home/<USER>/.mix
      - phoenix-dev-hex-cache:/home/<USER>/.hex
      - phoenix-dev-npm-cache:/home/<USER>/.npm
      - phoenix-dev-elixir-ls:/home/<USER>/.elixir_ls
      - /var/run/docker.sock:/var/run/docker.sock
    
    # Depends on database and cache services
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    
    # Network
    networks:
      - phoenix-dev-network
    
    # Working directory
    working_dir: /workspace
    
    # User
    user: vscode
    
    # Restart policy
    restart: unless-stopped

  # PostgreSQL database
  postgres:
    image: postgres:14-alpine
    container_name: phoenix-dev-postgres
    
    # Environment variables
    environment:
      POSTGRES_DB: phoenix_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      PGDATA: /var/lib/postgresql/data/pgdata
    
    # Port mapping
    ports:
      - "5432:5432"
    
    # Volume for data persistence
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    
    # Health check
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d phoenix_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    
    # Network
    networks:
      - phoenix-dev-network
    
    # Restart policy
    restart: unless-stopped

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: phoenix-dev-redis
    
    # Command with persistence
    command: redis-server --appendonly yes
    
    # Port mapping
    ports:
      - "6379:6379"
    
    # Volume for data persistence
    volumes:
      - redis-data:/data
    
    # Health check
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    
    # Network
    networks:
      - phoenix-dev-network
    
    # Restart policy
    restart: unless-stopped

# Named volumes for data persistence
volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  phoenix-dev-mix-cache:
    driver: local
  phoenix-dev-hex-cache:
    driver: local
  phoenix-dev-npm-cache:
    driver: local
  phoenix-dev-elixir-ls:
    driver: local

# Network for service communication
networks:
  phoenix-dev-network:
    driver: bridge
