{
  "name": "Phoenix Elixir with Kubernetes",
  "dockerComposeFile": "docker-compose.yml",
  "service": "phoenix-dev",
  "workspaceFolder": "/workspace",
  // Configure tool-specific properties
  "customizations": {
    "vscode": {
      // Extensions for Elixir/Phoenix development
      "extensions": [
        // Elixir Language Support
        "jakebecker.elixir-ls",
        "phoenixframework.phoenix",
        "bradlc.vscode-tailwindcss",
        // Database tools
        "ms-vscode.vscode-json",
        "ckolkman.vscode-postgres",
        "redis.redis-for-vscode",
        // Kubernetes and DevOps
        "ms-kubernetes-tools.vscode-kubernetes-tools",
        "ms-vscode.vscode-docker",
        "redhat.vscode-yaml",
        "ms-vscode.hexeditor",
        // Git and Version Control
        "eamodio.gitlens",
        "github.vscode-pull-request-github",
        // General Development
        "ms-vscode.vscode-json",
        "esbenp.prettier-vscode",
        "ms-vscode.live-server",
        "formulahendry.auto-rename-tag",
        // Testing and Debugging
        "ms-vscode.test-adapter-converter",
        "hbenl.vscode-test-explorer",
        // Productivity
        "ms-vscode.vscode-todo-highlight",
        "streetsidesoftware.code-spell-checker",
        "ms-vsliveshare.vsliveshare"
      ],
      // VS Code settings
      "settings": {
        // Elixir settings
        "elixirLS.dialyzerEnabled": true,
        "elixirLS.fetchDeps": true,
        "elixirLS.suggestSpecs": true,
        "elixirLS.signatureAfterComplete": true,
        // Editor settings
        "editor.tabSize": 2,
        "editor.insertSpaces": true,
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.fixAll": "explicit",
          "source.organizeImports": "explicit"
        },
        // File associations
        "files.associations": {
          "*.ex": "elixir",
          "*.exs": "elixir",
          "*.eex": "phoenix-heex",
          "*.heex": "phoenix-heex",
          "*.leex": "phoenix-heex"
        },
        // Terminal settings
        "terminal.integrated.defaultProfile.linux": "bash",
        "terminal.integrated.profiles.linux": {
          "bash": {
            "path": "/bin/bash"
          }
        },
        // Kubernetes settings
        "vs-kubernetes": {
          "vs-kubernetes.crd-code-completion": "enabled",
          "vs-kubernetes.kubectl-path.linux": "/usr/local/bin/kubectl"
        },
        // Docker settings
        "docker.dockerPath": "/usr/local/bin/docker",
        // Git settings
        "git.enableSmartCommit": true,
        "git.confirmSync": false,
        // Search settings
        "search.exclude": {
          "**/node_modules": true,
          "**/_build": true,
          "**/deps": true,
          "**/cover": true,
          "**/.elixir_ls": true
        },
        // File watcher settings
        "files.watcherExclude": {
          "**/_build/**": true,
          "**/deps/**": true,
          "**/node_modules/**": true,
          "**/.elixir_ls/**": true
        }
      }
    }
  },
  // Features to add to the dev container
  // Note: Using minimal features since we're on Alpine Linux
  // Most tools are installed directly in the Dockerfile
  "features": {},
  // Forward ports for Phoenix app and services
  "forwardPorts": [
    4000, // Phoenix application
    5432, // PostgreSQL
    6379, // Redis
    9090 // Metrics/Prometheus
  ],
  // Port attributes
  "portsAttributes": {
    "4000": {
      "label": "Phoenix App",
      "onAutoForward": "notify"
    },
    "5432": {
      "label": "PostgreSQL",
      "onAutoForward": "silent"
    },
    "6379": {
      "label": "Redis",
      "onAutoForward": "silent"
    },
    "9090": {
      "label": "Metrics",
      "onAutoForward": "silent"
    }
  },
  // Environment variables
  "containerEnv": {
    "MIX_ENV": "dev",
    "PHX_HOST": "localhost",
    "PORT": "4000",
    "DATABASE_URL": "ecto://postgres:postgres@postgres:5432/phoenix_dev",
    "REDIS_HOST": "redis",
    "REDIS_PORT": "6379",
    "LOG_LEVEL": "debug",
    "ENABLE_TELEMETRY": "true",
    "SECRET_KEY_BASE": "dev-secret-key-base-change-in-production",
    "LIVE_VIEW_SIGNING_SALT": "phoenix_live_view_dev"
  },
  // Lifecycle scripts
  "postCreateCommand": ".devcontainer/scripts/post-create.sh",
  "postStartCommand": ".devcontainer/scripts/post-start.sh",
  // Mount the local workspace and preserve command history
  "mounts": [
    "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached",
    "source=phoenix-dev-bashhistory,target=/home/<USER>/.bash_history,type=volume",
    "source=phoenix-dev-vscode-extensions,target=/home/<USER>/.vscode-server/extensions,type=volume"
  ],
  // Run arguments for docker-compose (not applicable when using dockerComposeFile)
  // "runArgs": ["--init"],
  // Override the default command to keep container running
  "overrideCommand": false,
  // User to run as
  "remoteUser": "vscode",
  // Shutdown action
  "shutdownAction": "stopCompose"
}
