#!/bin/bash

# Phoenix Elixir DevContainer Post-Start Script
# This script runs every time the container starts

set -e

echo "🔄 Starting Phoenix Elixir development environment..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if services are running
print_status "Checking service availability..."

# Check PostgreSQL
if pg_isready -h postgres -p 5432 -U postgres >/dev/null 2>&1; then
    print_success "PostgreSQL is available"
else
    print_warning "PostgreSQL is not yet available"
fi

# Check Redis
if redis-cli -h redis ping >/dev/null 2>&1; then
    print_success "Redis is available"
else
    print_warning "Redis is not yet available"
fi

# Update PATH for current session
export PATH="/usr/local/bin:$PATH"

# Check development tools
print_status "Verifying development tools..."

# Check GitHub CLI
if command -v gh >/dev/null 2>&1; then
    print_success "GitHub CLI is available ($(gh --version | head -n1 2>/dev/null || echo 'version check failed'))"
else
    print_warning "GitHub CLI is not available"
fi

# Check Kubernetes tools
if command -v kubectl >/dev/null 2>&1; then
    print_success "kubectl is available ($(kubectl version --client --short 2>/dev/null || echo 'version check failed'))"
else
    print_warning "kubectl is not available"
fi

if command -v helm >/dev/null 2>&1; then
    print_success "Helm is available ($(helm version --short 2>/dev/null || echo 'version check failed'))"
else
    print_warning "Helm is not available"
fi

if command -v k9s >/dev/null 2>&1; then
    print_success "k9s is available"
else
    print_warning "k9s is not available"
fi

# Check Elixir environment
print_status "Checking Elixir environment..."
if command -v elixir >/dev/null 2>&1; then
    print_success "Elixir $(elixir --version | grep Elixir | cut -d' ' -f2) is available"
else
    print_warning "Elixir is not available"
fi

if command -v mix >/dev/null 2>&1; then
    print_success "Mix is available"
else
    print_warning "Mix is not available"
fi

# Check Node.js environment
if command -v node >/dev/null 2>&1; then
    print_success "Node.js $(node --version) is available"
else
    print_warning "Node.js is not available"
fi

if command -v npm >/dev/null 2>&1; then
    print_success "npm $(npm --version) is available"
else
    print_warning "npm is not available"
fi

# Create scripts directory if it doesn't exist
mkdir -p scripts

# Display helpful information
echo ""
print_success "🎉 Development environment is ready!"
echo ""
print_status "Quick start commands:"
echo "  mix phx.server          - Start Phoenix server"
echo "  mix test               - Run tests"
echo "  mix deps.get           - Get dependencies"
echo "  mix ecto.migrate       - Run database migrations"
echo "  kubectl get pods       - Check Kubernetes pods"
echo "  k9s                    - Launch Kubernetes UI"
echo ""
print_status "Service endpoints:"
echo "  Phoenix App:    http://localhost:4000"
echo "  PostgreSQL:     localhost:5432"
echo "  Redis:          localhost:6379"
echo "  Metrics:        http://localhost:9090"
echo ""
print_status "Kubernetes local deployment:"
echo "  kubectl apply -k k8s/local/    - Deploy to local cluster"
echo "  kubectl get pods -n phoenix-app-local  - Check deployment status"
echo ""

# Check if we're in a Phoenix project
if [ -f "mix.exs" ]; then
    print_status "Phoenix project detected. You can start development with:"
    echo "  mix phx.server"
else
    print_warning "No mix.exs found. To create a new Phoenix project:"
    echo "  mix phx.new . --app phoenix_app"
fi

print_success "Ready for development! 🚀"
