-- Phoenix Elixir Development Database Initialization
-- This script runs when PostgreSQL container is first created

-- Create additional databases for testing
CREATE DATABASE phoenix_test;

-- Create a user for the application (optional, using default postgres user for simplicity)
-- CREATE USER phoenix_user WITH PASSWORD 'phoenix_password';
-- GRANT ALL PRIVILEGES ON DATABASE phoenix_dev TO phoenix_user;
-- GRANT ALL PRIVILEGES ON DATABASE phoenix_test TO phoenix_user;

-- Enable extensions that are commonly used in Phoenix applications
\c phoenix_dev;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "citext";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

\c phoenix_test;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "citext";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Log completion
\echo 'Database initialization completed successfully!'
