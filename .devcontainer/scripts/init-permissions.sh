#!/bin/bash

# Initialize permissions for vscode user
# This script runs as root to ensure proper permissions

set -e

echo "🔧 Initializing permissions for vscode user..."

# Ensure vscode user owns their home directory
chown -R vscode:vscode /home/<USER>

# Create VS Code server directories with proper permissions
mkdir -p /home/<USER>/.vscode-server/bin
mkdir -p /home/<USER>/.vscode-server/extensions
mkdir -p /home/<USER>/.local/bin
mkdir -p /home/<USER>/.mix
mkdir -p /home/<USER>/.hex
mkdir -p /home/<USER>/.npm
mkdir -p /home/<USER>/.elixir_ls

# Set proper ownership and permissions
chown -R vscode:vscode /home/<USER>
chmod -R 755 /home/<USER>

# Ensure workspace directory exists and has proper permissions
mkdir -p /workspace
chown -R vscode:vscode /workspace
chmod -R 755 /workspace

echo "✅ Permissions initialized successfully"
