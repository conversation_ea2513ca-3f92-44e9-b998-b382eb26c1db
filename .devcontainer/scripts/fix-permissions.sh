#!/bin/bash

# Fix permissions script for vscode user
# Run this if you encounter permission issues

echo "🔧 Fixing permissions for vscode user..."

# Fix ownership of home directory
sudo chown -R vscode:vscode /home/<USER>/dev/null || {
    echo "⚠️  Could not change ownership (running as non-root user)"
    echo "   This is normal if you're already the vscode user"
}

# Fix permissions
chmod -R 755 /home/<USER>/dev/null || {
    echo "⚠️  Could not change permissions for some files"
    echo "   This might be normal for some system files"
}

# Ensure VS Code server directories exist
mkdir -p /home/<USER>/.vscode-server/bin 2>/dev/null || true
mkdir -p /home/<USER>/.vscode-server/extensions 2>/dev/null || true

# Fix VS Code server permissions specifically
chmod -R 755 /home/<USER>/.vscode-server 2>/dev/null || true

# Fix workspace permissions
sudo chown -R vscode:vscode /workspace 2>/dev/null || {
    echo "⚠️  Could not change workspace ownership"
    echo "   Try running: sudo chown -R vscode:vscode /workspace"
}

echo "✅ Permission fix completed"
echo ""
echo "If you're still having issues, try:"
echo "  1. Restart the devcontainer"
echo "  2. Rebuild the devcontainer without cache"
echo "  3. Check Docker Desktop settings for file sharing permissions"
