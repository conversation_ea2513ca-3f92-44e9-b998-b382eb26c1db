# Phoenix Elixir Development Container

This directory contains a complete development container (devcontainer) configuration for Phoenix Elixir applications with Kubernetes support. The setup provides a fully configured development environment that includes all necessary tools for Phoenix development and Kubernetes deployment.

## 🚀 Features

### Development Environment
- **Elixir 1.15** with Phoenix framework support
- **Node.js and npm** for asset compilation
- **PostgreSQL 14** database with extensions
- **Redis 7** for caching and sessions
- **Elixir Language Server** for VS Code integration

### Kubernetes Tools
- **kubectl** - Kubernetes command-line tool
- **Helm** - Kubernetes package manager
- **k9s** - Terminal-based Kubernetes UI
- **kubectx/kubens** - Context and namespace switching
- **kustomize** - Kubernetes configuration management

### VS Code Extensions
- Elixir Language Support (`jakebecker.elixir-ls`)
- Phoenix Framework support
- Kubernetes tools integration
- Database management tools
- Git integration and productivity extensions

### Development Services
- PostgreSQL database with automatic initialization
- Redis cache server
- Persistent volumes for data and dependencies
- Health checks and service dependencies

## 📁 File Structure

```
.devcontainer/
├── README.md                 # This documentation
├── devcontainer.json        # Main devcontainer configuration
├── docker-compose.yml       # Multi-service development environment
├── Dockerfile              # Development container image
├── devcontainer.env        # Environment variables
└── scripts/
    ├── post-create.sh      # Setup script (runs once)
    ├── post-start.sh       # Startup script (runs on each start)
    └── init-db.sql         # Database initialization
```

## 🛠️ Getting Started

### Prerequisites
- **Docker Desktop** or **Docker Engine** with Docker Compose
- **Visual Studio Code** with the Dev Containers extension
- **Git** for version control

### Quick Start

1. **Clone or create your Phoenix project**:
   ```bash
   git clone <your-phoenix-repo>
   cd <your-phoenix-project>
   ```

2. **Open in VS Code**:
   ```bash
   code .
   ```

3. **Reopen in Container**:
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Dev Containers: Reopen in Container"
   - Select the command and wait for the container to build

4. **Wait for setup to complete**:
   - The container will build and install all dependencies
   - Services (PostgreSQL, Redis) will start automatically
   - Post-create scripts will set up the development environment

### For New Phoenix Projects

If you don't have an existing Phoenix project:

1. **Open the devcontainer** as described above
2. **Create a new Phoenix project**:
   ```bash
   mix phx.new . --app phoenix_app
   ```
3. **Set up the database**:
   ```bash
   mix ecto.setup
   ```
4. **Start the server**:
   ```bash
   mix phx.server
   ```

## 🔧 Configuration

### Environment Variables

The development environment uses these key environment variables (defined in `devcontainer.env`):

```bash
# Phoenix Configuration
MIX_ENV=dev
PHX_HOST=localhost
PORT=4000

# Database
DATABASE_URL=ecto://postgres:postgres@postgres:5432/phoenix_dev

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# Development secrets (change in production!)
SECRET_KEY_BASE=dev-secret-key-base-change-in-production
```

### Port Forwarding

The following ports are automatically forwarded:
- **4000**: Phoenix application
- **5432**: PostgreSQL database
- **6379**: Redis cache
- **9090**: Metrics/Prometheus

### VS Code Settings

The devcontainer includes optimized VS Code settings for Elixir development:
- Elixir Language Server with Dialyzer enabled
- Format on save
- Proper file associations for `.ex`, `.exs`, `.heex` files
- Kubernetes tools integration

## 🐳 Services

### Phoenix Development Container
- Based on `elixir:1.15-alpine`
- Includes all development tools and Kubernetes CLI tools
- Runs as `vscode` user with sudo access
- Persistent volumes for dependencies and caches

### PostgreSQL Database
- **Image**: `postgres:14-alpine`
- **Database**: `phoenix_dev` (and `phoenix_test` for testing)
- **Credentials**: `postgres:postgres`
- **Extensions**: uuid-ossp, citext, pg_trgm
- **Health checks**: Automatic readiness detection

### Redis Cache
- **Image**: `redis:7-alpine`
- **Persistence**: Enabled with AOF
- **Health checks**: Automatic ping checks

## 🎯 Development Workflow

### Daily Development

1. **Start the Phoenix server**:
   ```bash
   mix phx.server
   # or use the alias:
   phx
   ```

2. **Run tests**:
   ```bash
   mix test
   # or use the alias:
   phx-test
   ```

3. **Database operations**:
   ```bash
   mix ecto.migrate      # Run migrations
   mix ecto.rollback     # Rollback last migration
   mix ecto.reset        # Reset database
   ```

### Kubernetes Development

1. **Deploy to local Kubernetes**:
   ```bash
   kubectl apply -k k8s/local/
   ```

2. **Check deployment status**:
   ```bash
   kubectl get pods -n phoenix-app-local
   ```

3. **Use k9s for cluster management**:
   ```bash
   k9s
   ```

4. **View logs**:
   ```bash
   kubectl logs -f deployment/phoenix-app -n phoenix-app-local
   ```

### Useful Aliases

The development environment includes helpful aliases:

```bash
# Phoenix aliases
phx                    # mix phx.server
phx-test              # mix test --color
phx-deps              # mix deps.get && mix deps.compile
phx-setup             # mix setup
phx-reset             # mix ecto.reset

# Kubernetes aliases
k                     # kubectl
klocal                # kubectl --context=kind-kind
kdeploy               # kubectl apply -k k8s/local/
kclean                # kubectl delete -k k8s/local/
klogs                 # kubectl logs -f
kpods                 # kubectl get pods
```

## 🔍 Troubleshooting

### Container Won't Start
- Ensure Docker Desktop is running
- Check that ports 4000, 5432, 6379 are not in use
- Try rebuilding the container: "Dev Containers: Rebuild Container"

### Database Connection Issues
- Wait for PostgreSQL to be ready (check with `pg_isready -h postgres`)
- Verify DATABASE_URL environment variable
- Check if database exists: `psql -h postgres -U postgres -l`

### Elixir Language Server Issues
- Restart VS Code
- Clear Elixir LS cache: `rm -rf ~/.elixir_ls`
- Rebuild dependencies: `mix deps.clean --all && mix deps.get`

### Kubernetes Tools Not Working
- Verify tools are installed: `kubectl version`, `helm version`
- Check if local Kubernetes cluster is running
- Ensure proper context: `kubectl config current-context`

## � Advanced Configuration

### Custom Environment Variables
Add custom environment variables to `devcontainer.env` or modify `docker-compose.yml`:

```yaml
environment:
  CUSTOM_VAR: "custom_value"
```

### Additional VS Code Extensions
Add extensions to `devcontainer.json`:

```json
"extensions": [
  "your.extension.id"
]
```

### Custom Kubernetes Context
Configure your local Kubernetes context:

```bash
# For kind
kubectl config use-context kind-kind

# For minikube
kubectl config use-context minikube

# For Docker Desktop
kubectl config use-context docker-desktop
```

### Database Customization
Modify `scripts/init-db.sql` to add custom database setup:

```sql
-- Add custom extensions or initial data
CREATE EXTENSION IF NOT EXISTS "your_extension";
```

## 🚀 Performance Tips

### Speed Up Container Builds
- Use Docker BuildKit: `export DOCKER_BUILDKIT=1`
- Enable Docker Desktop's experimental features
- Use volume mounts for dependencies (already configured)

### Optimize Elixir Compilation
- Use persistent volumes for `_build` and `deps` directories
- Enable incremental compilation in development
- Use `mix compile --force` only when necessary

### Database Performance
- Increase shared_buffers for PostgreSQL if needed
- Use connection pooling (configured in Phoenix)
- Monitor query performance with `mix ecto.explain`

## �📚 Additional Resources

- [Phoenix Framework Documentation](https://hexdocs.pm/phoenix/)
- [Elixir Language Documentation](https://elixir-lang.org/docs.html)
- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [VS Code Dev Containers](https://code.visualstudio.com/docs/remote/containers)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Helm Documentation](https://helm.sh/docs/)

## 🤝 Contributing

To improve this devcontainer setup:

1. Fork the repository
2. Make your changes in a feature branch
3. Test the devcontainer thoroughly
4. Update documentation if needed
5. Submit a pull request with a clear description

## 📄 License

This devcontainer configuration is provided as-is for development purposes. Adjust security settings and secrets for production use.

---

**Happy coding with Phoenix and Kubernetes! 🚀**
