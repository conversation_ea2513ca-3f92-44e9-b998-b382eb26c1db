# Phoenix Elixir Application with Kubernetes

A complete Phoenix Elixir application setup with comprehensive Kubernetes deployment configurations and a fully-featured development container environment.

## 🚀 Quick Start

### Option 1: Development Container (Recommended)

The fastest way to get started is using the provided development container:

1. **Prerequisites**:
   - Docker Desktop or Docker Engine
   - Visual Studio Code with Dev Containers extension

2. **Open in Dev Container**:
   ```bash
   git clone <this-repo>
   cd <project-directory>
   code .
   ```
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Dev Containers: Reopen in Container"
   - Wait for the container to build and services to start

3. **Start Development**:
   ```bash
   # If this is a new project
   mix phx.new . --app phoenix_app
   
   # Set up the database
   mix ecto.setup
   
   # Start the Phoenix server
   mix phx.server
   ```

4. **Access Your Application**:
   - Phoenix App: http://localhost:4000
   - Database: localhost:5432
   - Redis: localhost:6379

### Option 2: Local Development

If you prefer to develop locally without containers:

1. **Install Dependencies**:
   - Elixir 1.15+
   - Node.js 18+
   - PostgreSQL 14+
   - Redis 7+

2. **Set up the Project**:
   ```bash
   mix deps.get
   mix ecto.setup
   mix phx.server
   ```

## 📁 Project Structure

```
├── .devcontainer/           # Development container configuration
│   ├── devcontainer.json   # VS Code devcontainer settings
│   ├── docker-compose.yml  # Development services
│   ├── Dockerfile          # Development environment
│   └── scripts/            # Setup and utility scripts
├── k8s/                    # Kubernetes deployment configurations
│   ├── local/              # Local development Kubernetes configs
│   ├── phoenix-*.yaml      # Phoenix application manifests
│   ├── postgres-*.yaml     # PostgreSQL database manifests
│   ├── redis-*.yaml        # Redis cache manifests
│   └── README.md           # Kubernetes deployment guide
└── README.md               # This file
```

## 🐳 Development Container Features

The development container provides a complete development environment with:

### Development Tools
- **Elixir 1.15** with Phoenix framework
- **Node.js and npm** for asset compilation
- **PostgreSQL 14** with common extensions
- **Redis 7** for caching and sessions
- **Elixir Language Server** for VS Code

### Kubernetes Tools
- **kubectl** - Kubernetes CLI
- **Helm** - Package manager for Kubernetes
- **k9s** - Terminal-based Kubernetes UI
- **kubectx/kubens** - Context and namespace switching
- **kustomize** - Configuration management

### VS Code Integration
- Pre-configured extensions for Elixir/Phoenix development
- Kubernetes tools and YAML support
- Database management tools
- Git integration and productivity extensions

## ☸️ Kubernetes Deployment

This project includes comprehensive Kubernetes configurations for both local development and production deployment.

### Local Kubernetes Development

1. **Set up a local Kubernetes cluster** (choose one):
   ```bash
   # Docker Desktop (enable Kubernetes)
   # OR
   # Minikube
   minikube start
   # OR
   # kind
   kind create cluster
   ```

2. **Deploy to local Kubernetes**:
   ```bash
   kubectl apply -k k8s/local/
   ```

3. **Check deployment status**:
   ```bash
   kubectl get pods -n phoenix-app-local
   ```

4. **Access the application**:
   ```bash
   kubectl port-forward service/phoenix-service 4000:4000 -n phoenix-app-local
   ```

### Production Deployment

The `k8s/` directory contains production-ready Kubernetes manifests for:
- Phoenix application with horizontal pod autoscaling
- PostgreSQL database with persistent storage
- Redis cache cluster
- Ingress with TLS termination
- Network policies and security configurations
- Monitoring and observability

See `k8s/README.md` for detailed deployment instructions.

## 🛠️ Development Workflow

### Daily Development Commands

```bash
# Start Phoenix server
mix phx.server
# or use the alias (in devcontainer):
phx

# Run tests
mix test
# or use the alias:
phx-test

# Database operations
mix ecto.migrate      # Run migrations
mix ecto.rollback     # Rollback migrations
mix ecto.reset        # Reset database

# Dependency management
mix deps.get          # Get dependencies
mix deps.update --all # Update all dependencies
```

### Kubernetes Development Commands

```bash
# Deploy to local Kubernetes
kubectl apply -k k8s/local/

# Check status
kubectl get pods -n phoenix-app-local

# View logs
kubectl logs -f deployment/phoenix-app -n phoenix-app-local

# Use k9s for cluster management
k9s

# Clean up deployment
kubectl delete -k k8s/local/
```

## 🔧 Configuration

### Environment Variables

Key environment variables for development:

```bash
# Phoenix
MIX_ENV=dev
PHX_HOST=localhost
PORT=4000

# Database
DATABASE_URL=ecto://postgres:postgres@postgres:5432/phoenix_dev

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# Secrets (development only!)
SECRET_KEY_BASE=your-secret-key-base
LIVE_VIEW_SIGNING_SALT=your-signing-salt
```

### Database Configuration

The development environment includes:
- PostgreSQL 14 with common extensions (uuid-ossp, citext, pg_trgm)
- Automatic database creation and initialization
- Separate test database
- Health checks and connection pooling

### Cache Configuration

Redis is configured for:
- Session storage
- Application caching with Nebulex
- Background job queues (if using Oban)

## 🧪 Testing

```bash
# Run all tests
mix test

# Run tests with coverage
mix test --cover

# Run specific test file
mix test test/my_app_web/controllers/page_controller_test.exs

# Run tests in watch mode (if using mix_test_watch)
mix test.watch
```

## 📊 Monitoring and Observability

The application includes:
- Health check endpoints (`/health`, `/ready`)
- Prometheus metrics on port 9090
- Telemetry integration
- Structured logging
- Database query monitoring

## 🚀 Deployment

### Local Development
- Use the development container for the best experience
- All services are automatically configured and started
- Hot reloading and debugging support

### Staging/Production
- Use the Kubernetes manifests in `k8s/`
- Configure secrets and environment variables appropriately
- Set up monitoring and alerting
- Configure ingress and TLS certificates

## 📚 Documentation

- [Development Container Setup](.devcontainer/README.md)
- [Kubernetes Deployment Guide](k8s/README.md)
- [Phoenix Framework Documentation](https://hexdocs.pm/phoenix/)
- [Elixir Documentation](https://elixir-lang.org/docs.html)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Update documentation
7. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Ready to build amazing Phoenix applications with Kubernetes! 🚀**
