use Mix.Config

# Configure your database
config :phoenix_app, PhoenixApp.Repo,
  username: "postgres",
  password: "postgres",
  database: "phoenix_dev",
  hostname: "localhost",
  pool_size: 10

# For development, we disable any cache and enable
# debugging and code reloading.
config :phoenix_app, PhoenixAppWeb.Endpoint,
  http: [port: 4000],
  debug_errors: true,
  code_reloader: true,
  check_origin: false,
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:default, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:default, ~w(--watch)]}
  ]

# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# Mix task:
#
#     mix phx.gen.cert
#
# Run `mix help phx.gen.cert` for more information.
#
# The `http:` config above can be replaced with:
#
#     https: [
#       port: 4001,
#       cipher_suite: :strong,
#       keyfile: "priv/cert/selfsigned_key.pem",
#       certfile: "priv/cert/selfsigned.pem"
#     ]
#
# For production, it's recommended to configure a
# reverse proxy to handle the SSL encryption instead.
# See the `config/prod.exs` for more information.

# Do not include metadata nor timestamps in development logs

config :logger, :console, format: "[$level] $message\n"

