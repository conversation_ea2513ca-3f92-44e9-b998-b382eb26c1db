{
  // Elixir Language Server settings
  "elixirLS.dialyzerEnabled": true,
  "elixirLS.fetchDeps": true,
  "elixirLS.suggestSpecs": true,
  "elixirLS.signatureAfterComplete": true,
  "elixirLS.enableTestLenses": true,
  // Editor settings
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.organizeImports": "explicit"
  },
  // File associations
  "files.associations": {
    "*.ex": "elixir",
    "*.exs": "elixir",
    "*.eex": "phoenix-heex",
    "*.heex": "phoenix-heex",
    "*.leex": "phoenix-heex",
    "*.yaml": "yaml",
    "*.yml": "yaml"
  },
  // Search and file watcher exclusions
  "search.exclude": {
    "**/node_modules": true,
    "**/_build": true,
    "**/deps": true,
    "**/cover": true,
    "**/.elixir_ls": true,
    "**/priv/static": true
  },
  "files.watcherExclude": {
    "**/_build/**": true,
    "**/deps/**": true,
    "**/node_modules/**": true,
    "**/.elixir_ls/**": true,
    "**/priv/static/**": true
  },
  // Terminal settings
  "terminal.integrated.defaultProfile.linux": "bash",
  "terminal.integrated.profiles.linux": {
    "bash": {
      "path": "/bin/bash"
    },
    "zsh": {
      "path": "/bin/zsh"
    }
  },
  // Git settings
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,
  // Kubernetes settings
  "vs-kubernetes": {
    "vs-kubernetes.crd-code-completion": "enabled",
    "vs-kubernetes.kubectl-path.linux": "/usr/local/bin/kubectl"
  },
  // YAML settings for Kubernetes manifests
  "yaml.schemas": {
    "https://raw.githubusercontent.com/instrumenta/kubernetes-json-schema/master/v1.18.0-standalone-strict/all.json": [
      "k8s/**/*.yaml",
      "k8s/**/*.yml"
    ]
  },
  // Docker settings
  "docker.dockerPath": "/usr/local/bin/docker",
  // Phoenix/Elixir specific settings
  "emmet.includeLanguages": {
    "phoenix-heex": "html",
    "elixir": "html"
  },
  // Test settings
  "elixirLS.testTimeoutMs": 30000,
  // Formatting settings
  "elixirLS.mixEnv": "dev",
  "elixirLS.projectDir": "",
  // LiveView settings
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  // Tailwind CSS settings (if using Tailwind)
  "tailwindCSS.includeLanguages": {
    "phoenix-heex": "html",
    "elixir": "html"
  },
  "tailwindCSS.experimental.classRegex": [
    "class[:]\\s*\"([^\"]*)"
  ]
}
