#!/bin/bash

# Phoenix Elixir DevContainer Post-Create Script
# This script runs after the container is created

set -e

echo "🚀 Setting up Phoenix Elixir development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Wait for services to be ready
print_status "Waiting for PostgreSQL to be ready..."
until pg_isready -h postgres -p 5432 -U postgres; do
    echo "Waiting for PostgreSQL..."
    sleep 2
done
print_success "PostgreSQL is ready!"

print_status "Waiting for Redis to be ready..."
until redis-cli -h redis ping; do
    echo "Waiting for Redis..."
    sleep 2
done
print_success "Redis is ready!"

# Check if this is a new Phoenix project or existing one
if [ ! -f "mix.exs" ]; then
    print_warning "No mix.exs found. This appears to be a new project."
    print_status "Creating example Phoenix project structure..."

    # Create a basic Phoenix project structure for reference
    mkdir -p lib config priv test assets

    # Create a basic mix.exs template
    cat > mix.exs << 'EOF'
defmodule PhoenixApp.MixProject do
  use Mix.Project

  def project do
    [
      app: :phoenix_app,
      version: "0.1.0",
      elixir: "~> 1.15",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps()
    ]
  end

  def application do
    [
      mod: {PhoenixApp.Application, []},
      extra_applications: [:logger, :runtime_tools]
    ]
  end

  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  defp deps do
    [
      {:phoenix, "~> 1.7.0"},
      {:phoenix_ecto, "~> 4.4"},
      {:ecto_sql, "~> 3.10"},
      {:postgrex, ">= 0.0.0"},
      {:phoenix_html, "~> 3.3"},
      {:phoenix_live_reload, "~> 1.2", only: :dev},
      {:phoenix_live_view, "~> 0.19.0"},
      {:floki, ">= 0.30.0", only: :test},
      {:phoenix_live_dashboard, "~> 0.8.0"},
      {:esbuild, "~> 0.7", runtime: Mix.env() == :dev},
      {:tailwind, "~> 0.2.0", runtime: Mix.env() == :dev},
      {:swoosh, "~> 1.3"},
      {:finch, "~> 0.13"},
      {:telemetry_metrics, "~> 0.6"},
      {:telemetry_poller, "~> 1.0"},
      {:gettext, "~> 0.20"},
      {:jason, "~> 1.2"},
      {:plug_cowboy, "~> 2.5"},
      {:nebulex, "~> 2.4"},
      {:nebulex_redis_adapter, "~> 2.3"}
    ]
  end

  defp aliases do
    [
      setup: ["deps.get", "ecto.setup", "assets.setup", "assets.build"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.setup": ["tailwind.install --if-missing", "esbuild.install --if-missing"],
      "assets.build": ["tailwind default", "esbuild default"],
      "assets.deploy": ["tailwind default --minify", "esbuild default --minify", "phx.digest"]
    ]
  end
end
EOF

    print_success "Created basic mix.exs template"

    # Create basic config structure
    mkdir -p config
    cat > config/config.exs << 'EOF'
import Config

# Configure your database
config :phoenix_app, PhoenixApp.Repo,
  username: System.get_env("DB_USERNAME", "postgres"),
  password: System.get_env("DB_PASSWORD", "postgres"),
  hostname: System.get_env("DB_HOSTNAME", "localhost"),
  database: System.get_env("DB_NAME", "phoenix_dev"),
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: String.to_integer(System.get_env("DB_POOL_SIZE", "10"))

# Configure the endpoint
config :phoenix_app, PhoenixAppWeb.Endpoint,
  url: [host: "localhost"],
  render_errors: [view: PhoenixAppWeb.ErrorView, accepts: ~w(html json), layout: false],
  pubsub_server: PhoenixApp.PubSub,
  live_view: [signing_salt: System.get_env("LIVE_VIEW_SIGNING_SALT", "phoenix_live_view")]

# Configure esbuild and tailwind
config :esbuild,
  version: "0.17.11",
  default: [
    args: ~w(js/app.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

config :tailwind,
  version: "3.3.0",
  default: [
    args: ~w(
      --config=tailwind.config.js
      --input=css/app.css
      --output=../priv/static/assets/app.css
    ),
    cd: Path.expand("../assets", __DIR__)
  ]

# Import environment specific config
import_config "#{config_env()}.exs"
EOF

    print_success "Created basic config structure"

else
    print_success "Found existing Phoenix project!"

    # Install/update dependencies
    print_status "Installing Elixir dependencies..."
    mix deps.get || print_warning "Failed to get dependencies - you may need to run 'mix deps.get' manually"

    # Compile dependencies
    print_status "Compiling dependencies..."
    mix deps.compile || print_warning "Failed to compile dependencies"

    # Install Node.js dependencies if assets directory exists
    if [ -d "assets" ]; then
        print_status "Installing Node.js dependencies..."
        cd assets && npm install && cd ..
    fi

    # Set up database
    print_status "Setting up database..."
    mix ecto.create || print_warning "Database may already exist"
    mix ecto.migrate || print_warning "No migrations to run or migration failed"
fi

# Verify development tools are available
print_status "Verifying development tools..."

# Check GitHub CLI
if command -v gh >/dev/null 2>&1; then
    print_success "GitHub CLI is available ($(gh --version | head -n1 2>/dev/null || echo 'version check failed'))"
else
    print_warning "GitHub CLI is not available"
fi

# Check Kubernetes tools
if command -v kubectl >/dev/null 2>&1; then
    print_success "kubectl is available ($(kubectl version --client --short 2>/dev/null || echo 'version check failed'))"
else
    print_warning "kubectl is not available"
fi

if command -v helm >/dev/null 2>&1; then
    print_success "Helm is available ($(helm version --short 2>/dev/null || echo 'version check failed'))"
else
    print_warning "Helm is not available"
fi

if command -v k9s >/dev/null 2>&1; then
    print_success "k9s is available"
else
    print_warning "k9s is not available"
fi

if command -v kustomize >/dev/null 2>&1; then
    print_success "kustomize is available"
else
    print_warning "kustomize is not available"
fi

# Set up git hooks if .git directory exists
if [ -d ".git" ]; then
    print_status "Setting up git hooks..."
    mkdir -p .git/hooks

    # Pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Run tests and formatting before commit
echo "Running pre-commit checks..."

# Format code
mix format --check-formatted || {
    echo "Code is not formatted. Run 'mix format' to fix."
    exit 1
}

# Run tests
mix test || {
    echo "Tests failed. Fix tests before committing."
    exit 1
}

echo "Pre-commit checks passed!"
EOF

    chmod +x .git/hooks/pre-commit
    print_success "Git hooks configured"
fi

# Create useful development aliases
print_status "Setting up development aliases..."
cat >> ~/.bashrc << 'EOF'

# Phoenix Development Aliases
alias phx="mix phx.server"
alias phx-test="mix test --color"
alias phx-deps="mix deps.get && mix deps.compile"
alias phx-setup="mix setup"
alias phx-reset="mix ecto.reset"
alias phx-migrate="mix ecto.migrate"
alias phx-rollback="mix ecto.rollback"
alias phx-routes="mix phx.routes"
alias phx-format="mix format"

# Kubernetes Development Aliases
alias k="kubectl"
alias klocal="kubectl --context=kind-kind"  # Adjust for your local cluster
alias kdeploy="kubectl apply -k k8s/local/"
alias kclean="kubectl delete -k k8s/local/"
alias klogs="kubectl logs -f"
alias kpods="kubectl get pods"
alias ksvc="kubectl get services"

# Docker aliases
alias dps="docker ps"
alias dimg="docker images"
alias dlog="docker logs -f"

EOF

print_success "Development aliases configured"

# Create development helper scripts
print_status "Creating development helper scripts..."

# Database reset script
cat > scripts/reset-db.sh << 'EOF'
#!/bin/bash
echo "Resetting database..."
mix ecto.drop
mix ecto.create
mix ecto.migrate
mix run priv/repo/seeds.exs
echo "Database reset complete!"
EOF

# Test script
cat > scripts/test.sh << 'EOF'
#!/bin/bash
echo "Running tests..."
mix test --color
EOF

# Local Kubernetes deployment script
cat > scripts/deploy-local.sh << 'EOF'
#!/bin/bash
echo "Deploying to local Kubernetes..."
kubectl apply -k k8s/local/
echo "Deployment complete!"
echo "Check status with: kubectl get pods -n phoenix-app-local"
EOF

chmod +x scripts/*.sh 2>/dev/null || true

print_success "Helper scripts created in scripts/ directory"

# Final setup message
print_success "🎉 Phoenix Elixir development environment setup complete!"
echo ""
print_status "Next steps:"
echo "  1. If this is a new project, run: mix phx.new . --app phoenix_app"
echo "  2. Start the Phoenix server: mix phx.server"
echo "  3. Visit http://localhost:4000 to see your application"
echo "  4. Use 'k9s' for Kubernetes cluster management"
echo "  5. Use 'kubectl apply -k k8s/local/' to deploy to local Kubernetes"
echo ""
print_status "Available commands:"
echo "  - phx: Start Phoenix server"
echo "  - phx-test: Run tests"
echo "  - phx-setup: Set up the project"
echo "  - k: kubectl shortcut"
echo "  - k9s: Kubernetes UI"
echo ""
print_success "Happy coding! 🚀"
